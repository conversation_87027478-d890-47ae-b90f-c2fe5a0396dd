/* 隐藏列表连接线但保留项目图标 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏列表项之间的垂直连接线（但保留项目符号） */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线 */
.markdown-preview-view .list-bullet::after,
.markdown-preview-view .list-number::after {
    display: none !important;
}

/* ===== 无序列表子列表符号设计 ===== */

/* 预览模式下的无序列表符号层级设计 */
.markdown-preview-view ul {
    list-style-type: none;
}

/* 第一层：实心圆点 */
.markdown-preview-view ul > li::before {
    content: "●";
    color: #666;
    font-weight: normal;
    position: absolute;
    left: -1.2em;
    font-size: 0.6em;
}

/* 第二层：空心圆点 */
.markdown-preview-view ul ul > li::before {
    content: "○";
    color: #888;
    font-weight: normal;
    font-size: 0.6em;
}

/* 第三层：实心方块 */
.markdown-preview-view ul ul ul > li::before {
    content: "■";
    color: #999;
    font-size: 0.6em;
}

/* 第四层：空心方块 */
.markdown-preview-view ul ul ul ul > li::before {
    content: "□";
    color: #aaa;
    font-size: 0.6em;
}

/* 第五层：菱形 */
.markdown-preview-view ul ul ul ul ul > li::before {
    content: "◆";
    color: #bbb;
    font-size: 0.6em;
}

/* 第六层及以上：三角形 */
.markdown-preview-view ul ul ul ul ul ul > li::before {
    content: "▶";
    color: #ccc;
    font-size: 0.6em;
}

/* 确保列表项有足够的左边距来显示符号 */
.markdown-preview-view ul li {
    position: relative;
    margin-left: 1.5em;
}

/* 编辑模式下的无序列表符号设计 */
/* 隐藏原始的列表符号 */
.cm-editor .cm-formatting-list-ul {
    color: transparent !important;
    position: relative;
}

/* 通用编辑模式符号样式 */
.cm-editor .cm-formatting-list-ul::after {
    position: absolute;
    left: 0;
    font-size: 0.6em;
    color: #666;
}

/* 第一层编辑模式符号 - 使用缩进级别判断 */
.cm-editor .cm-line:not(.cm-indent) .cm-formatting-list-ul::after {
    content: "●";
    color: #666;
}

/* 第二层编辑模式符号 */
.cm-editor .cm-line.cm-indent .cm-formatting-list-ul::after {
    content: "○";
    color: #888;
}

/* 第三层编辑模式符号 */
.cm-editor .cm-line.cm-indent .cm-indent .cm-formatting-list-ul::after {
    content: "■";
    color: #999;
}

/* 第四层编辑模式符号 */
.cm-editor .cm-line.cm-indent .cm-indent .cm-indent .cm-formatting-list-ul::after {
    content: "□";
    color: #aaa;
}

/* 第五层编辑模式符号 */
.cm-editor .cm-line.cm-indent .cm-indent .cm-indent .cm-indent .cm-formatting-list-ul::after {
    content: "◆";
    color: #bbb;
}

/* 第六层及以上编辑模式符号 */
.cm-editor .cm-line.cm-indent .cm-indent .cm-indent .cm-indent .cm-indent .cm-formatting-list-ul::after {
    content: "▶";
    color: #ccc;
}

/* 备用方案：使用数据属性检测层级 */
.cm-editor .cm-line[data-indent="0"] .cm-formatting-list-ul::after {
    content: "●";
    color: #666;
}

.cm-editor .cm-line[data-indent="1"] .cm-formatting-list-ul::after {
    content: "○";
    color: #888;
}

.cm-editor .cm-line[data-indent="2"] .cm-formatting-list-ul::after {
    content: "■";
    color: #999;
}

.cm-editor .cm-line[data-indent="3"] .cm-formatting-list-ul::after {
    content: "□";
    color: #aaa;
}

.cm-editor .cm-line[data-indent="4"] .cm-formatting-list-ul::after {
    content: "◆";
    color: #bbb;
}

.cm-editor .cm-line[data-indent="5"] .cm-formatting-list-ul::after,
.cm-editor .cm-line[data-indent="6"] .cm-formatting-list-ul::after,
.cm-editor .cm-line[data-indent="7"] .cm-formatting-list-ul::after {
    content: "▶";
    color: #ccc;
}