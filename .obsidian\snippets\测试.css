/* 隐藏列表连接线但保留项目图标 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏列表项之间的垂直连接线（但保留项目符号） */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线 */
.markdown-preview-view .list-bullet::after,
.markdown-preview-view .list-number::after {
    display: none !important;
}

/* ===== 无序列表子列表符号设计 ===== */

/* 预览模式下的无序列表符号层级设计 */
/* 精确替换原始符号位置 */

/* 重置所有无序列表的默认样式 */
.markdown-preview-view ul {
    list-style: none;
    padding-left: 0;
}

.markdown-preview-view ul li {
    position: relative;
    padding-left: 1.5em;
}

/* 第一层：实心圆点 */
.markdown-preview-view ul > li::marker {
    content: none;
}
.markdown-preview-view ul > li::before {
    content: "●";
    color: #666;
    position: absolute;
    left: 0.5em;
    top: 0;
}

/* 第二层：空心圆点 */
.markdown-preview-view ul ul > li::before {
    content: "○";
    color: #888;
}

/* 第三层：实心方块 */
.markdown-preview-view ul ul ul > li::before {
    content: "■";
    color: #999;
}

/* 第四层：空心方块 */
.markdown-preview-view ul ul ul ul > li::before {
    content: "□";
    color: #aaa;
}

/* 第五层：菱形 */
.markdown-preview-view ul ul ul ul ul > li::before {
    content: "◆";
    color: #bbb;
}

/* 第六层及以上：三角形 */
.markdown-preview-view ul ul ul ul ul ul > li::before {
    content: "▶";
    color: #ccc;
}

/* 编辑模式下的无序列表符号设计 */
/* 使用文本替换方法，保持原始位置 */

/* 通用设置：隐藏原始符号并准备替换 */
.cm-editor .cm-formatting-list-ul {
    position: relative;
    color: transparent !important;
    text-indent: 0 !important;
}

.cm-editor .cm-formatting-list-ul::after {
    position: absolute;
    left: 0;
    top: 0;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* 第一层：实心圆点 */
.cm-editor .HyperMD-list-line-1 .cm-formatting-list-ul::after {
    content: "●";
    color: #666 !important;
}

/* 第二层：空心圆点 */
.cm-editor .HyperMD-list-line-2 .cm-formatting-list-ul::after {
    content: "○";
    color: #888 !important;
}

/* 第三层：实心方块 */
.cm-editor .HyperMD-list-line-3 .cm-formatting-list-ul::after {
    content: "■";
    color: #999 !important;
}

/* 第四层：空心方块 */
.cm-editor .HyperMD-list-line-4 .cm-formatting-list-ul::after {
    content: "□";
    color: #aaa !important;
}

/* 第五层：菱形 */
.cm-editor .HyperMD-list-line-5 .cm-formatting-list-ul::after {
    content: "◆";
    color: #bbb !important;
}

/* 第六层及以上：三角形 */
.cm-editor .HyperMD-list-line-6 .cm-formatting-list-ul::after,
.cm-editor .HyperMD-list-line-7 .cm-formatting-list-ul::after,
.cm-editor .HyperMD-list-line-8 .cm-formatting-list-ul::after,
.cm-editor .HyperMD-list-line-9 .cm-formatting-list-ul::after {
    content: "▶";
    color: #ccc !important;
}

/* 备用方案：如果HyperMD类不工作，使用默认符号 */
.cm-editor .cm-formatting-list-ul:not([class*="HyperMD"])::after {
    content: "●";
    color: #666 !important;
}