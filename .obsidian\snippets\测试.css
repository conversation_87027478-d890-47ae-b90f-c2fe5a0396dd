/* 隐藏列表连接线但保留项目图标 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏列表项之间的垂直连接线（但保留项目符号） */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线 */
.markdown-preview-view .list-bullet::after,
.markdown-preview-view .list-number::after {
    display: none !important;
}

/* ===== 无序列表子列表符号设计 ===== */

/* 预览模式下的无序列表符号层级设计 */
/* 直接修改默认列表符号类型 */

/* 第一层：实心圆点 */
.markdown-preview-view ul > li {
    list-style-type: "●";
    color: #666;
}
.markdown-preview-view ul > li::marker {
    color: #666;
}

/* 第二层：空心圆点 */
.markdown-preview-view ul ul > li {
    list-style-type: "○";
}
.markdown-preview-view ul ul > li::marker {
    color: #888;
}

/* 第三层：实心方块 */
.markdown-preview-view ul ul ul > li {
    list-style-type: "■";
}
.markdown-preview-view ul ul ul > li::marker {
    color: #999;
}

/* 第四层：空心方块 */
.markdown-preview-view ul ul ul ul > li {
    list-style-type: "□";
}
.markdown-preview-view ul ul ul ul > li::marker {
    color: #aaa;
}

/* 第五层：菱形 */
.markdown-preview-view ul ul ul ul ul > li {
    list-style-type: "◆";
}
.markdown-preview-view ul ul ul ul ul > li::marker {
    color: #bbb;
}

/* 第六层及以上：三角形 */
.markdown-preview-view ul ul ul ul ul ul > li {
    list-style-type: "▶";
}
.markdown-preview-view ul ul ul ul ul ul > li::marker {
    color: #ccc;
}

/* 恢复列表项文本颜色 */
.markdown-preview-view ul li {
    color: var(--text-normal);
}

/* 编辑模式下的无序列表符号设计 */
/* 直接替换符号内容，保持原始位置和大小 */

/* 第一层：实心圆点 */
.cm-editor .HyperMD-list-line-1 .cm-formatting-list-ul {
    color: #666 !important;
}
.cm-editor .HyperMD-list-line-1 .cm-formatting-list-ul::before {
    content: "●";
    position: absolute;
    left: 0;
}

/* 第二层：空心圆点 */
.cm-editor .HyperMD-list-line-2 .cm-formatting-list-ul {
    color: #888 !important;
}
.cm-editor .HyperMD-list-line-2 .cm-formatting-list-ul::before {
    content: "○";
    position: absolute;
    left: 0;
}

/* 第三层：实心方块 */
.cm-editor .HyperMD-list-line-3 .cm-formatting-list-ul {
    color: #999 !important;
}
.cm-editor .HyperMD-list-line-3 .cm-formatting-list-ul::before {
    content: "■";
    position: absolute;
    left: 0;
}

/* 第四层：空心方块 */
.cm-editor .HyperMD-list-line-4 .cm-formatting-list-ul {
    color: #aaa !important;
}
.cm-editor .HyperMD-list-line-4 .cm-formatting-list-ul::before {
    content: "□";
    position: absolute;
    left: 0;
}

/* 第五层：菱形 */
.cm-editor .HyperMD-list-line-5 .cm-formatting-list-ul {
    color: #bbb !important;
}
.cm-editor .HyperMD-list-line-5 .cm-formatting-list-ul::before {
    content: "◆";
    position: absolute;
    left: 0;
}

/* 第六层及以上：三角形 */
.cm-editor .HyperMD-list-line-6 .cm-formatting-list-ul,
.cm-editor .HyperMD-list-line-7 .cm-formatting-list-ul,
.cm-editor .HyperMD-list-line-8 .cm-formatting-list-ul,
.cm-editor .HyperMD-list-line-9 .cm-formatting-list-ul {
    color: #ccc !important;
}
.cm-editor .HyperMD-list-line-6 .cm-formatting-list-ul::before,
.cm-editor .HyperMD-list-line-7 .cm-formatting-list-ul::before,
.cm-editor .HyperMD-list-line-8 .cm-formatting-list-ul::before,
.cm-editor .HyperMD-list-line-9 .cm-formatting-list-ul::before {
    content: "▶";
    position: absolute;
    left: 0;
}

/* 隐藏原始符号，显示新符号 */
.cm-editor .cm-formatting-list-ul {
    position: relative;
    color: transparent !important;
}