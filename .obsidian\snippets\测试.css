/* 隐藏列表连接线但保留项目图标 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏列表项之间的垂直连接线（但保留项目符号） */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线 */
.markdown-preview-view .list-bullet::after,
.markdown-preview-view .list-number::after {
    display: none !important;
}

/* ===== 无序列表子列表符号设计 ===== */

/* 预览模式下的无序列表符号层级设计 */
.markdown-preview-view ul {
    list-style-type: none;
}

/* 第一层：实心圆点 */
.markdown-preview-view ul > li::before {
    content: "●";
    color: #666;
    font-weight: normal;
    position: absolute;
    left: -0.8em;
    top: 0.1em;
    font-size: 0.6em;
    line-height: 1;
}

/* 第二层：空心圆点 */
.markdown-preview-view ul ul > li::before {
    content: "○";
    color: #888;
    font-weight: normal;
    font-size: 0.6em;
    position: absolute;
    left: -0.8em;
    top: 0.1em;
    line-height: 1;
}

/* 第三层：实心方块 */
.markdown-preview-view ul ul ul > li::before {
    content: "■";
    color: #999;
    font-size: 0.6em;
    position: absolute;
    left: -0.8em;
    top: 0.1em;
    line-height: 1;
}

/* 第四层：空心方块 */
.markdown-preview-view ul ul ul ul > li::before {
    content: "□";
    color: #aaa;
    font-size: 0.6em;
    position: absolute;
    left: -0.8em;
    top: 0.1em;
    line-height: 1;
}

/* 第五层：菱形 */
.markdown-preview-view ul ul ul ul ul > li::before {
    content: "◆";
    color: #bbb;
    font-size: 0.6em;
    position: absolute;
    left: -0.8em;
    top: 0.1em;
    line-height: 1;
}

/* 第六层及以上：三角形 */
.markdown-preview-view ul ul ul ul ul ul > li::before {
    content: "▶";
    color: #ccc;
    font-size: 0.6em;
    position: absolute;
    left: -0.8em;
    top: 0.1em;
    line-height: 1;
}

/* 确保列表项有足够的左边距来显示符号 */
.markdown-preview-view ul li {
    position: relative;
    margin-left: 1em;
}

/* 编辑模式下的无序列表符号设计 */

/* 隐藏原始符号 */
.cm-editor .cm-formatting-list-ul {
    position: relative;
    color: transparent !important;
}

/* 通用符号样式 */
.cm-editor .cm-formatting-list-ul::after {
    font-size: 0.6em;
    position: absolute;
    left: 0;
    top: 0.1em;
    line-height: 1;
    opacity: 1;
    visibility: visible;
}

/* 第一层：基于HyperMD-list-line-1类 */
.cm-editor .HyperMD-list-line-1 .cm-formatting-list-ul::after {
    content: "●" !important;
    color: #666 !important;
}

/* 第二层：基于HyperMD-list-line-2类 */
.cm-editor .HyperMD-list-line-2 .cm-formatting-list-ul::after {
    content: "○" !important;
    color: #888 !important;
}

/* 第三层：基于HyperMD-list-line-3类 */
.cm-editor .HyperMD-list-line-3 .cm-formatting-list-ul::after {
    content: "■" !important;
    color: #999 !important;
}

/* 第四层：基于HyperMD-list-line-4类 */
.cm-editor .HyperMD-list-line-4 .cm-formatting-list-ul::after {
    content: "□" !important;
    color: #aaa !important;
}

/* 第五层：基于HyperMD-list-line-5类 */
.cm-editor .HyperMD-list-line-5 .cm-formatting-list-ul::after {
    content: "◆" !important;
    color: #bbb !important;
}

/* 第六层及以上：基于HyperMD-list-line-6+类 */
.cm-editor .HyperMD-list-line-6 .cm-formatting-list-ul::after,
.cm-editor .HyperMD-list-line-7 .cm-formatting-list-ul::after,
.cm-editor .HyperMD-list-line-8 .cm-formatting-list-ul::after,
.cm-editor .HyperMD-list-line-9 .cm-formatting-list-ul::after {
    content: "▶" !important;
    color: #ccc !important;
}

/* 备用方案：基于缩进数量检测 */
.cm-editor .cm-line:not([class*="HyperMD-list-line"]) .cm-formatting-list-ul::after {
    content: "●";
    color: #666;
}

/* 基于tab缩进的检测 */
.cm-editor .cm-line[style*="text-indent"] .cm-formatting-list-ul::after {
    content: "○";
    color: #888;
}

/* 基于多重缩进的检测 */
.cm-editor .cm-line[style*="margin-left: 2"] .cm-formatting-list-ul::after,
.cm-editor .cm-line[style*="margin-left: 3"] .cm-formatting-list-ul::after {
    content: "○";
    color: #888;
}

.cm-editor .cm-line[style*="margin-left: 4"] .cm-formatting-list-ul::after,
.cm-editor .cm-line[style*="margin-left: 5"] .cm-formatting-list-ul::after {
    content: "■";
    color: #999;
}