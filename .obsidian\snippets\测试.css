/* 隐藏列表连接线但保留项目图标 */

/* 隐藏列表折叠/展开的连接线指示器 */
.markdown-preview-view .list-collapse-indicator,
.markdown-source-view .list-collapse-indicator,
.cm-editor .list-collapse-indicator {
    display: none !important;
}

/* 隐藏编辑模式下的缩进连接线 */
.cm-editor .cm-indent-markers .cm-indent-marker {
    display: none !important;
}

/* 隐藏列表项之间的垂直连接线（但保留项目符号） */
.HyperMD-list-line .cm-formatting-list::after {
    display: none !important;
}

/* 隐藏嵌套列表的连接线 */
.cm-editor .cm-line .cm-indent::before {
    content: none !important;
}

/* 隐藏预览模式下的列表连接线 */
.markdown-preview-view .list-bullet::after,
.markdown-preview-view .list-number::after {
    display: none !important;
}

/* ===== 无序列表子列表符号设计 ===== */

/* 预览模式下的无序列表符号层级设计 */
.markdown-preview-view ul {
    list-style-type: none;
}

/* 第一层：实心圆点 */
.markdown-preview-view ul > li::before {
    content: "●";
    color: #666;
    font-weight: normal;
    position: absolute;
    left: -1em;
    font-size: 0.8em;
    line-height: 1.6;
}

/* 第二层：空心圆点 */
.markdown-preview-view ul ul > li::before {
    content: "○";
    color: #888;
    font-weight: normal;
    font-size: 0.8em;
    position: absolute;
    left: -1em;
    line-height: 1.6;
}

/* 第三层：实心方块 */
.markdown-preview-view ul ul ul > li::before {
    content: "■";
    color: #999;
    font-size: 0.8em;
    position: absolute;
    left: -1em;
    line-height: 1.6;
}

/* 第四层：空心方块 */
.markdown-preview-view ul ul ul ul > li::before {
    content: "□";
    color: #aaa;
    font-size: 0.8em;
    position: absolute;
    left: -1em;
    line-height: 1.6;
}

/* 第五层：菱形 */
.markdown-preview-view ul ul ul ul ul > li::before {
    content: "◆";
    color: #bbb;
    font-size: 0.8em;
    position: absolute;
    left: -1em;
    line-height: 1.6;
}

/* 第六层及以上：三角形 */
.markdown-preview-view ul ul ul ul ul ul > li::before {
    content: "▶";
    color: #ccc;
    font-size: 0.8em;
    position: absolute;
    left: -1em;
    line-height: 1.6;
}

/* 确保列表项有足够的左边距来显示符号 */
.markdown-preview-view ul li {
    position: relative;
    margin-left: 1.2em;
}

/* 编辑模式下的无序列表符号设计 */
/* 使用正确的Obsidian编辑模式选择器 */

/* 第一层编辑模式符号（无缩进） */
.cm-editor .HyperMD-list-line:not(.cm-indent) .cm-formatting-list-ul::before {
    content: "●";
    color: #666;
    font-size: 0.8em;
    position: absolute;
    left: 0;
    line-height: 1.6;
}

/* 第二层编辑模式符号（一级缩进） */
.cm-editor .HyperMD-list-line.cm-indent .cm-formatting-list-ul::before {
    content: "○";
    color: #888;
    font-size: 0.8em;
    position: absolute;
    left: 0;
    line-height: 1.6;
}

/* 第三层编辑模式符号（二级缩进） */
.cm-editor .HyperMD-list-line.cm-indent .cm-indent .cm-formatting-list-ul::before {
    content: "■";
    color: #999;
    font-size: 0.8em;
    position: absolute;
    left: 0;
    line-height: 1.6;
}

/* 第四层编辑模式符号（三级缩进） */
.cm-editor .HyperMD-list-line.cm-indent .cm-indent .cm-indent .cm-formatting-list-ul::before {
    content: "□";
    color: #aaa;
    font-size: 0.8em;
    position: absolute;
    left: 0;
    line-height: 1.6;
}

/* 第五层编辑模式符号（四级缩进） */
.cm-editor .HyperMD-list-line.cm-indent .cm-indent .cm-indent .cm-indent .cm-formatting-list-ul::before {
    content: "◆";
    color: #bbb;
    font-size: 0.8em;
    position: absolute;
    left: 0;
    line-height: 1.6;
}

/* 第六层及以上编辑模式符号（五级及以上缩进） */
.cm-editor .HyperMD-list-line.cm-indent .cm-indent .cm-indent .cm-indent .cm-indent .cm-formatting-list-ul::before {
    content: "▶";
    color: #ccc;
    font-size: 0.8em;
    position: absolute;
    left: 0;
    line-height: 1.6;
}

/* 隐藏原始的列表符号 */
.cm-editor .cm-formatting-list-ul {
    color: transparent !important;
    position: relative;
}